import { apiClient } from '../api';
import { AxiosError } from 'axios';

// TypeScript interfaces for 2FA operations
export interface SendEmail2FARequest {
  email: string;
}

export interface SendEmail2FAResponse {
  success: boolean;
  message: string;
  data?: {
    email: string;
    codeExpiry?: string;
    requestId?: string;
  };
}

export interface VerifyEmail2FARequest {
  email: string;
  code: string;
}

export interface VerifyEmail2FAResponse {
  success: boolean;
  message: string;
  data?: {
    email: string;
    twoFactorEnabled: boolean;
    backupCodes?: string[];
  };
}

export interface TwoFactorError {
  message: string;
  code?: string;
  details?: any;
}

/**
 * Send verification code to user's email for 2FA setup
 * @param email - User's email address
 * @returns Promise with API response
 */
export const sendEmail2FA = async (email: string): Promise<SendEmail2FAResponse> => {
  try {
    console.log(`Sending 2FA verification code to email: ${email}`);

    const requestData: SendEmail2FARequest = {
      email: email.trim().toLowerCase()
    };

    const response = await apiClient.post('/api/v1/auth/sendEmail2FA', requestData);

    if (response.status === 200 || response.status === 201) {
      console.log('2FA email sent successfully:', response.data);
      return {
        success: true,
        message: response.data.message || 'Verification code sent to your email',
        data: response.data.data || { email }
      };
    }

    // Handle unexpected success status codes
    console.warn('Unexpected response status:', response.status);
    return {
      success: false,
      message: 'Unexpected response from server'
    };

  } catch (error) {
    console.error('Error sending 2FA email:', error);
    
    if (error instanceof AxiosError) {
      const errorMessage = error.response?.data?.message || 
                          error.response?.data?.error || 
                          'Failed to send verification code';
      
      const errorCode = error.response?.data?.code || error.response?.status?.toString();
      
      return {
        success: false,
        message: errorMessage,
        data: { email }
      };
    }

    return {
      success: false,
      message: 'Network error occurred while sending verification code'
    };
  }
};

/**
 * Verify email 2FA code and enable two-factor authentication
 * @param email - User's email address
 * @param code - Verification code from email
 * @returns Promise with API response
 */
export const verifyEmail2FA = async (email: string, code: string): Promise<VerifyEmail2FAResponse> => {
  try {
    console.log(`Verifying 2FA code for email: ${email}`);

    const requestData: VerifyEmail2FARequest = {
      email: email.trim().toLowerCase(),
      code: code.trim()
    };

    const response = await apiClient.post('/api/v1/auth/verifyEmail2FA', requestData);

    if (response.status === 200 || response.status === 201) {
      console.log('2FA verification successful:', response.data);
      return {
        success: true,
        message: response.data.message || 'Two-factor authentication enabled successfully',
        data: response.data.data || { 
          email, 
          twoFactorEnabled: true 
        }
      };
    }

    // Handle unexpected success status codes
    console.warn('Unexpected response status:', response.status);
    return {
      success: false,
      message: 'Unexpected response from server'
    };

  } catch (error) {
    console.error('Error verifying 2FA code:', error);
    
    if (error instanceof AxiosError) {
      const errorMessage = error.response?.data?.message || 
                          error.response?.data?.error || 
                          'Failed to verify code';
      
      const errorCode = error.response?.data?.code || error.response?.status?.toString();
      
      // Handle specific error cases
      if (error.response?.status === 400) {
        return {
          success: false,
          message: errorMessage || 'Invalid verification code'
        };
      }
      
      if (error.response?.status === 404) {
        return {
          success: false,
          message: 'Verification code not found or expired'
        };
      }
      
      if (error.response?.status === 429) {
        return {
          success: false,
          message: 'Too many attempts. Please try again later'
        };
      }
      
      return {
        success: false,
        message: errorMessage
      };
    }

    return {
      success: false,
      message: 'Network error occurred while verifying code'
    };
  }
};

/**
 * Check if 2FA is enabled for the current user
 * @returns Promise with 2FA status
 */
export const check2FAStatus = async (): Promise<{ enabled: boolean; email?: string }> => {
  try {
    const response = await apiClient.get('/api/v1/auth/2fa/status');
    
    if (response.status === 200) {
      return {
        enabled: response.data.enabled || false,
        email: response.data.email
      };
    }
    
    return { enabled: false };
  } catch (error) {
    console.error('Error checking 2FA status:', error);
    return { enabled: false };
  }
};

/**
 * Disable 2FA for the current user
 * @returns Promise with disable operation result
 */
export const disable2FA = async (): Promise<{ success: boolean; message: string }> => {
  try {
    const response = await apiClient.post('/api/v1/auth/2fa/disable');
    
    if (response.status === 200) {
      return {
        success: true,
        message: response.data.message || 'Two-factor authentication disabled successfully'
      };
    }
    
    return {
      success: false,
      message: 'Failed to disable two-factor authentication'
    };
  } catch (error) {
    console.error('Error disabling 2FA:', error);
    
    if (error instanceof AxiosError) {
      const errorMessage = error.response?.data?.message || 
                          error.response?.data?.error || 
                          'Failed to disable two-factor authentication';
      return {
        success: false,
        message: errorMessage
      };
    }
    
    return {
      success: false,
      message: 'Network error occurred while disabling 2FA'
    };
  }
};

/**
 * Validate email format
 * @param email - Email to validate
 * @returns boolean indicating if email is valid
 */
export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email.trim());
};

/**
 * Validate verification code format
 * @param code - Code to validate
 * @returns boolean indicating if code format is valid
 */
export const validateVerificationCode = (code: string): boolean => {
  // Assuming 6-digit numeric code
  const codeRegex = /^\d{6}$/;
  return codeRegex.test(code.trim());
};
